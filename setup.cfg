[metadata]
name = Django
version = attr: django.__version__
url = https://www.djangoproject.com/
author = Django Software Foundation
author_email = <EMAIL>
description = A high-level Python web framework that encourages rapid development and clean, pragmatic design.
long_description = file: README.rst
license = BSD-3-Clause
classifiers =
    Development Status :: 2 - Pre-Alpha
    Environment :: Web Environment
    Framework :: Django
    Intended Audience :: Developers
    License :: OSI Approved :: BSD License
    Operating System :: OS Independent
    Programming Language :: Python
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3 :: Only
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Topic :: Internet :: WWW/HTTP
    Topic :: Internet :: WWW/HTTP :: Dynamic Content
    Topic :: Internet :: WWW/HTTP :: WSGI
    Topic :: Software Development :: Libraries :: Application Frameworks
    Topic :: Software Development :: Libraries :: Python Modules
project_urls =
    Documentation = https://docs.djangoproject.com/
    Release notes = https://docs.djangoproject.com/en/stable/releases/
    Funding = https://www.djangoproject.com/fundraising/
    Source = https://github.com/django/django
    Tracker = https://code.djangoproject.com/

[options]
python_requires = >=3.10
packages = find:
include_package_data = true
zip_safe = false
install_requires =
    asgiref >= 3.6.0
    sqlparse >= 0.2.2
    tzdata; sys_platform == 'win32'

[options.entry_points]
console_scripts =
    django-admin = django.core.management:execute_from_command_line

[options.extras_require]
argon2 = argon2-cffi >= 19.1.0
bcrypt = bcrypt

[bdist_rpm]
doc_files = docs extras AUTHORS INSTALL LICENSE README.rst
install_script = scripts/rpm-install.sh

[flake8]
exclude = build,.git,.tox,./tests/.env
extend-ignore = E203
max-line-length = 88
per-file-ignores =
    django/core/cache/backends/filebased.py:W601
    django/core/cache/backends/base.py:W601
    django/core/cache/backends/redis.py:W601
    tests/cache/tests.py:W601

[isort]
profile = black
default_section = THIRDPARTY
known_first_party = django
